<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whitebye.angel.ai.mapper.AiAgentNotesMapper">

    <resultMap id="BaseResultMap" type="com.whitebye.angel.ai.common.entity.AiAgentNotesDO">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_name" property="agentName"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="category" property="category"/>
        <result column="tags" property="tags"/>
        <result column="priority" property="priority"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, agent_id, agent_name, title, content, category, tags, priority, create_time, update_time, deleted
    </sql>

    <insert id="insert" parameterType="com.whitebye.angel.ai.common.entity.AiAgentNotesDO">
        INSERT INTO ai_agent_notes (
            agent_id, agent_name, title, content, category, tags, priority,
            create_time, update_time, deleted
        ) VALUES (
            #{agentId}, #{agentName}, #{title}, #{content}, #{category}, #{tags}, #{priority},
            NOW(), NOW(), 0
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ai_agent_notes (
            agent_id, agent_name, title, content, category, tags, priority,
            create_time, update_time, deleted
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.agentId}, #{item.agentName}, #{item.title}, #{item.content}, #{item.category}, #{item.tags}, #{item.priority},
                NOW(), NOW(), 0
            )
        </foreach>
    </insert>

    <update id="fakeDelete">
        UPDATE ai_agent_notes
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="batchFakeDelete">
        UPDATE ai_agent_notes
        SET deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateById" parameterType="com.whitebye.angel.ai.common.entity.AiAgentNotesDO">
        UPDATE ai_agent_notes
        <set>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="agentName != null">agent_name = #{agentName},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="category != null">category = #{category},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="priority != null">priority = #{priority},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="batchUpdateByIds" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE ai_agent_notes
            <set>
                <if test="item.agentId != null">agent_id = #{item.agentId},</if>
                <if test="item.agentName != null">agent_name = #{item.agentName},</if>
                <if test="item.title != null">title = #{item.title},</if>
                <if test="item.content != null">content = #{item.content},</if>
                <if test="item.category != null">category = #{item.category},</if>
                <if test="item.tags != null">tags = #{item.tags},</if>
                <if test="item.priority != null">priority = #{item.priority},</if>
                update_time = NOW()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByParam" resultMap="BaseResultMap" parameterType="com.whitebye.angel.ai.common.query.AiAgentNotesQuery">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_agent_notes
        WHERE deleted = 0
        <if test="agentId != null">
            AND agent_id = #{agentId}
        </if>
        <if test="(title != null and title != '') or (content != null and content != '') or (category != null and category != '') or (tags != null and tags != '')">
            AND (
            <if test="title != null and title != ''">
                title LIKE CONCAT('%', #{title}, '%')
                <if test="content != null and content != '' or category != null and category != '' or tags != null and tags != ''"> OR </if>
            </if>
            <if test="content != null and content != ''">
                content LIKE CONCAT('%', #{content}, '%')
                <if test="category != null and category != '' or tags != null and tags != ''"> OR </if>
            </if>
            <if test="category != null and category != ''">
                category LIKE CONCAT('%', #{category}, '%')
                <if test="tags != null and tags != ''"> OR </if>
            </if>
            <if test="tags != null and tags != ''">
                tags LIKE CONCAT('%', #{tags}, '%')
            </if>
            )
        </if>
    </select>

</mapper>