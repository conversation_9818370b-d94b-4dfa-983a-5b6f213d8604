package com.whitebye.angel.ai.mapper;

import com.whitebye.angel.ai.common.entity.AiAgentNotesDO;
import com.whitebye.angel.ai.common.query.AiAgentNotesQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AiAgentNotesMapper {

    int insert(AiAgentNotesDO record);

    int batchInsert(List<AiAgentNotesDO> list);

    int fakeDelete(@Param("id") Long id);

    int batchFakeDelete(@Param("ids") List<Long> ids);

    int updateById(AiAgentNotesDO record);

    int batchUpdateByIds(List<AiAgentNotesDO> list);

    List<AiAgentNotesDO> selectByParam(AiAgentNotesQuery query);
}