package com.whitebye.angel.ai.controller;

import com.whitebye.angel.ai.biz.service.AiAgentNotesService;
import com.whitebye.angel.ai.common.BizResult;
import com.whitebye.angel.ai.common.req.*;
import com.whitebye.angel.ai.common.rsp.AiAgentNotesRsp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Deprecated
@RestController
public class AiAgentNotesController {

    @Resource
    private AiAgentNotesService aiAgentNotesService;

    /**
     * 新增笔记
     */
    @PostMapping(value = "/ai/agent/notes/add")
    public BizResult<Boolean> aiAgentNotesAdd(@Valid @RequestBody AiAgentNotesAddReq req) {
        return BizResult.success(aiAgentNotesService.aiAgentNotesAdd(req));
    }

    /**
     * 批量新增笔记
     */
    @PostMapping(value = "/ai/agent/notes/batch/add")
    public BizResult<Boolean> aiAgentNotesBatchAdd(@Valid @RequestBody AiAgentNotesBatchAddReq req) {
        return BizResult.success(aiAgentNotesService.aiAgentNotesBatchAdd(req));
    }

    /**
     * 修改笔记
     */
    @PostMapping(value = "/ai/agent/notes/modify")
    public BizResult<Boolean> aiAgentNotesModify(@Valid @RequestBody AiAgentNotesModifyReq req) {
        return BizResult.success(aiAgentNotesService.aiAgentNotesModify(req));
    }

    /**
     * 批量修改笔记
     */
    @PostMapping(value = "/ai/agent/notes/batch/modify")
    public BizResult<Boolean> aiAgentNotesBatchModify(@Valid @RequestBody AiAgentNotesBatchModifyReq req) {
        return BizResult.success(aiAgentNotesService.aiAgentNotesBatchModify(req));
    }

    /**
     * 删除笔记
     */
    @PostMapping(value = "/ai/agent/notes/remove")
    public BizResult<Boolean> aiAgentNotesRemove(@Valid @RequestBody AiAgentNotesRemoveReq req) {
        return BizResult.success(aiAgentNotesService.aiAgentNotesRemove(req));
    }

    /**
     * 查询笔记
     */
    @PostMapping(value = "/ai/agent/notes/query")
    public BizResult<List<AiAgentNotesRsp>> aiAgentNotesQuery(@Valid @RequestBody AiAgentNotesQueryReq req) {
        return BizResult.success(aiAgentNotesService.aiAgentNotesQuery(req));
    }
} 