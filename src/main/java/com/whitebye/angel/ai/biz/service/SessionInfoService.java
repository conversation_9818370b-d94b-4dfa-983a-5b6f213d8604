package com.whitebye.angel.ai.biz.service;

import com.whitebye.angel.ai.common.req.*;
import com.whitebye.angel.ai.common.rsp.SessionInfoRsp;

import java.util.List;

@Deprecated
public interface SessionInfoService {

    /**
     * 新增会话
     */
    Boolean sessionInfoAdd(List<SessionInfoAddReq> reqList);

    /**
     * 修改会话
     */
    Boolean sessionInfoModify(List<SessionInfoModifyReq> reqList);

    /**
     * 删除会话
     */
    Boolean sessionInfoRemove(SessionInfoRemoveReq req);

    /**
     * 查询会话
     */
    List<SessionInfoRsp> sessionInfoQuery(SessionInfoQueryReq req);

} 