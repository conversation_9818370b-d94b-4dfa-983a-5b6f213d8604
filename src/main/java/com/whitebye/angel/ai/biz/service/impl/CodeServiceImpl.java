package com.whitebye.angel.ai.biz.service.impl;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.EnumDeclaration;
import com.github.javaparser.ast.body.AnnotationDeclaration;
import com.whitebye.angel.ai.biz.service.CodeService;
import com.whitebye.angel.ai.common.entity.AppInfoDO;
import com.whitebye.angel.ai.common.entity.ClassCodeDO;
import com.whitebye.angel.ai.common.entity.MethodCodeDO;
import com.whitebye.angel.ai.common.query.AppInfoQuery;
import com.whitebye.angel.ai.common.query.ClassCodeQuery;
import com.whitebye.angel.ai.common.query.CodeSearchQuery;
import com.whitebye.angel.ai.common.query.MethodCodeQuery;
import com.whitebye.angel.ai.common.req.QueryCodeInfoReq;
import com.whitebye.angel.ai.common.rsp.QueryCodeInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.github.javaparser.ParseResult;
import java.util.Map;
import com.whitebye.angel.ai.common.entity.CodeSearchDO;
import com.whitebye.angel.ai.common.req.ExecuteHttpReq;
import com.whitebye.angel.ai.common.rsp.ExecuteHttpRsp;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import java.util.Collections;

@Deprecated

@Slf4j
@Service
public class CodeServiceImpl implements CodeService {

    @Resource
    private AppInfoManager appInfoManager;

    @Resource
    private ClassCodeManager classCodeManager;

    @Resource
    private MethodCodeManager methodCodeManager;

    @Resource
    private CodeSearchManager codeSearchManager;

    @Resource
    private RestTemplate restTemplate;

    @Value("${git.username}")
    private String gitUsername;

    @Value("${git.password}")
    private String gitPassword;

    @Override
    public Boolean analyzeCode() {
        List<AppInfoDO> appInfoDOList = queryAllAppInfo();
        if (CollectionUtils.isEmpty(appInfoDOList)) {
            return Boolean.FALSE;
        }

        appInfoDOList.forEach(appInfoDO -> {
            log.info("开始解析{}应用代码", appInfoDO.getAppName());
            String appName = appInfoDO.getAppName();
            String codeRepositoryUrl = appInfoDO.getCodeRepositoryUrl();
            Long appId = appInfoDO.getId();
            // 1. 拉取git仓库代码到临时目录
            String localPath = System.getProperty("java.io.tmpdir") + "/angel-ai/" + appName;
            File localDir = new File(localPath);
            if (localDir.exists()) {
                // 删除旧目录
                deleteDir(localDir);
            }
            try {
                Git.cloneRepository()
                        .setURI(codeRepositoryUrl)
                        .setDirectory(localDir)
                        .setCredentialsProvider(new UsernamePasswordCredentialsProvider(gitUsername, gitPassword))
                        .call();
            } catch (Exception e) {
                log.error("拉取代码仓库失败: {}", codeRepositoryUrl, e);
                return;
            }
            // 递归查找所有src/main/java目录
            List<File> srcDirs = new ArrayList<>();
            findSrcMainJavaDirs(localDir, srcDirs);
            if (srcDirs.isEmpty()) {
                log.warn("未找到任何src/main/java目录: {}", localDir.getAbsolutePath());
                return;
            }
            List<ClassCodeDO> classCodeDOList = new ArrayList<>();
            List<MethodCodeDO> methodCodeDOList = new ArrayList<>();
            for (File srcDir : srcDirs) {
                List<File> javaFiles = new ArrayList<>();
                collectJavaFiles(srcDir, javaFiles);
                for (File javaFile : javaFiles) {
                    try (FileInputStream fis = new FileInputStream(javaFile)) {
                        JavaParser javaParser = new JavaParser();
                        ParseResult<CompilationUnit> result = javaParser.parse(fis, StandardCharsets.UTF_8);
                        if (!result.isSuccessful() || !result.getResult().isPresent()) {
                            log.error("JavaParser解析失败: {}", javaFile.getAbsolutePath());
                            continue;
                        }
                        CompilationUnit cu = result.getResult().get();
                        String packageName = cu.getPackageDeclaration().map(pd -> pd.getName().asString()).orElse("");
                        for (ClassOrInterfaceDeclaration clazz : cu.findAll(ClassOrInterfaceDeclaration.class)) {
                            // 获取完整类名（父类.内部类）
                            String className = getFullClassName(clazz);
                            String classCode = clazz.toString();
                            ClassCodeDO classCodeDO = new ClassCodeDO();
                            classCodeDO.setAppId(appId);
                            classCodeDO.setAppName(appName);
                            classCodeDO.setPackageName(packageName);
                            classCodeDO.setClassName(className);
                            classCodeDO.setClassCode(classCode);
                            classCodeDO.setCodeLength(classCode.length());
                            classCodeDO.setCodeDate(Integer.valueOf(new java.text.SimpleDateFormat("yyyyMMdd").format(new Date())));
                            classCodeDO.setDescribe("");
                            classCodeDO.setCreateTime(new Date());
                            classCodeDO.setUpdateTime(new Date());
                            classCodeDO.setDeleted(0);
                            classCodeDO.setClassType(clazz.isInterface() ? 2 : 1); // 1=类，2=接口
                            classCodeDOList.add(classCodeDO);
                            // 方法
                            clazz.getMethods().forEach(method -> {
                                MethodCodeDO methodCodeDO = new MethodCodeDO();
                                methodCodeDO.setAppId(appId);
                                methodCodeDO.setAppName(appName);
                                methodCodeDO.setPackageName(packageName);
                                methodCodeDO.setClassName(className);
                                methodCodeDO.setMethodName(method.getNameAsString());
                                methodCodeDO.setMethodCode(method.toString());
                                methodCodeDO.setCodeLength(method.toString().length());
                                methodCodeDO.setCodeDate(Integer.valueOf(new java.text.SimpleDateFormat("yyyyMMdd").format(new Date())));
                                methodCodeDO.setDescribe("");
                                methodCodeDO.setCreateTime(new Date());
                                methodCodeDO.setUpdateTime(new Date());
                                methodCodeDO.setDeleted(0);
                                methodCodeDOList.add(methodCodeDO);
                            });
                        }
                        // 处理枚举
                        for (EnumDeclaration enumDecl : cu.findAll(EnumDeclaration.class)) {
                            String enumName = enumDecl.getNameAsString();
                            String enumCode = enumDecl.toString();
                            ClassCodeDO classCodeDO = new ClassCodeDO();
                            classCodeDO.setAppId(appId);
                            classCodeDO.setAppName(appName);
                            classCodeDO.setPackageName(packageName);
                            classCodeDO.setClassName(enumName);
                            classCodeDO.setClassCode(enumCode);
                            classCodeDO.setCodeLength(enumCode.length());
                            classCodeDO.setCodeDate(Integer.valueOf(new java.text.SimpleDateFormat("yyyyMMdd").format(new Date())));
                            classCodeDO.setDescribe("");
                            classCodeDO.setCreateTime(new Date());
                            classCodeDO.setUpdateTime(new Date());
                            classCodeDO.setDeleted(0);
                            classCodeDO.setClassType(3); // 3=枚举
                            classCodeDOList.add(classCodeDO);
                            // 枚举下方法
                            enumDecl.getMethods().forEach(method -> {
                                MethodCodeDO methodCodeDO = new MethodCodeDO();
                                methodCodeDO.setAppId(appId);
                                methodCodeDO.setAppName(appName);
                                methodCodeDO.setPackageName(packageName);
                                methodCodeDO.setClassName(enumName);
                                methodCodeDO.setMethodName(method.getNameAsString());
                                methodCodeDO.setMethodCode(method.toString());
                                methodCodeDO.setCodeLength(method.toString().length());
                                methodCodeDO.setCodeDate(Integer.valueOf(new java.text.SimpleDateFormat("yyyyMMdd").format(new Date())));
                                methodCodeDO.setDescribe("");
                                methodCodeDO.setCreateTime(new Date());
                                methodCodeDO.setUpdateTime(new Date());
                                methodCodeDO.setDeleted(0);
                                methodCodeDOList.add(methodCodeDO);
                            });
                        }
                        // 处理注解
                        for (AnnotationDeclaration annotationDecl : cu.findAll(AnnotationDeclaration.class)) {
                            String annotationName = annotationDecl.getNameAsString();
                            String annotationCode = annotationDecl.toString();
                            ClassCodeDO classCodeDO = new ClassCodeDO();
                            classCodeDO.setAppId(appId);
                            classCodeDO.setAppName(appName);
                            classCodeDO.setPackageName(packageName);
                            classCodeDO.setClassName(annotationName);
                            classCodeDO.setClassCode(annotationCode);
                            classCodeDO.setCodeLength(annotationCode.length());
                            classCodeDO.setCodeDate(Integer.valueOf(new java.text.SimpleDateFormat("yyyyMMdd").format(new Date())));
                            classCodeDO.setDescribe("");
                            classCodeDO.setCreateTime(new Date());
                            classCodeDO.setUpdateTime(new Date());
                            classCodeDO.setDeleted(0);
                            classCodeDO.setClassType(4); // 4=注解
                            classCodeDOList.add(classCodeDO);
                            // 注解下方法（理论上注解无方法，但为一致性保留）
                            annotationDecl.getMethods().forEach(method -> {
                                MethodCodeDO methodCodeDO = new MethodCodeDO();
                                methodCodeDO.setAppId(appId);
                                methodCodeDO.setAppName(appName);
                                methodCodeDO.setPackageName(packageName);
                                methodCodeDO.setClassName(annotationName);
                                methodCodeDO.setMethodName(method.getNameAsString());
                                methodCodeDO.setMethodCode(method.toString());
                                methodCodeDO.setCodeLength(method.toString().length());
                                methodCodeDO.setCodeDate(Integer.valueOf(new java.text.SimpleDateFormat("yyyyMMdd").format(new Date())));
                                methodCodeDO.setDescribe("");
                                methodCodeDO.setCreateTime(new Date());
                                methodCodeDO.setUpdateTime(new Date());
                                methodCodeDO.setDeleted(0);
                                methodCodeDOList.add(methodCodeDO);
                            });
                        }
                    } catch (Exception e) {
                        log.error("解析Java文件失败: {}", javaFile.getAbsolutePath(), e);
                    }
                }
            }
            // 3. 先批量保存ClassCodeDO，回填id
            if (!classCodeDOList.isEmpty()) {
                log.info("准备保存{}个类", classCodeDOList.size());
                classCodeManager.save(classCodeDOList);
            }
            // 4. 构建classKey->classId映射（方案B，跳过重复并日志提示）
            Map<String, Long> classKeyToId = new java.util.HashMap<>();
            for (ClassCodeDO c : classCodeDOList) {
                String key = c.getAppId() + "#" + c.getPackageName() + "#" + c.getClassName();
                if (classKeyToId.containsKey(key)) {
                    log.warn("Duplicate class key: {}, id1={}, id2={}", key, classKeyToId.get(key), c.getId());
                    continue;
                }
                classKeyToId.put(key, c.getId());
            }
            // 5. 遍历MethodCodeDO，设置classId
            for (MethodCodeDO m : methodCodeDOList) {
                String key = m.getAppId() + "#" + m.getPackageName() + "#" + m.getClassName();
                m.setClassId(classKeyToId.get(key));
            }
            // 6. 批量保存方法
            if (!methodCodeDOList.isEmpty()) {
                log.info("准备保存{}个方法", methodCodeDOList.size());
                methodCodeManager.save(methodCodeDOList);
            }
        });
        return Boolean.TRUE;
    }

    @Override
    public Boolean analyzeSearchCode() {
        List<CodeSearchDO> searchList = new ArrayList<>();

        // 查询所有AppInfoDO
        List<AppInfoDO> appList = appInfoManager.query(new AppInfoQuery());
        for (AppInfoDO appInfoDO : appList) {
            if (StringUtils.isBlank(appInfoDO.getDescribe())) {
                continue;
            }

            CodeSearchQuery query = new CodeSearchQuery();
            query.setAppId(appInfoDO.getId());
            if (CollectionUtils.isNotEmpty(codeSearchManager.query(query))) {
                continue;
            }

            CodeSearchDO search = new CodeSearchDO();
            search.setDescribe(appInfoDO.getDescribe());
            search.setAppId(appInfoDO.getId());
            search.setAppName(appInfoDO.getAppName());
            searchList.add(search);
        }

        // 查询所有ClassCodeDO
        List<ClassCodeDO> classList = classCodeManager.query(new ClassCodeQuery());
        for (ClassCodeDO classCodeDO : classList) {
            if (StringUtils.isBlank(classCodeDO.getDescribe())) {
                continue;
            }

            CodeSearchQuery query = new CodeSearchQuery();
            query.setAppId(classCodeDO.getAppId());
            query.setClassId(classCodeDO.getId());
            if (CollectionUtils.isNotEmpty(codeSearchManager.query(query))) {
                continue;
            }

            CodeSearchDO search = new CodeSearchDO();
            search.setDescribe(classCodeDO.getDescribe());
            search.setAppId(classCodeDO.getAppId());
            search.setAppName(classCodeDO.getAppName());
            search.setClassId(classCodeDO.getId());
            search.setPackageName(classCodeDO.getPackageName());
            search.setClassName(classCodeDO.getClassName());
            searchList.add(search);
        }

        // 查询所有MethodCodeDO
        List<MethodCodeDO> methodList = methodCodeManager.query(new MethodCodeQuery());
        for (MethodCodeDO methodCodeDO : methodList) {
            if (StringUtils.isBlank(methodCodeDO.getDescribe())) {
                continue;
            }

            CodeSearchQuery query = new CodeSearchQuery();
            query.setAppId(methodCodeDO.getAppId());
            query.setClassId(methodCodeDO.getClassId());
            query.setMethodId(methodCodeDO.getId());
            if (CollectionUtils.isNotEmpty(codeSearchManager.query(query))) {
                continue;
            }

            CodeSearchDO search = new CodeSearchDO();
            search.setDescribe(methodCodeDO.getDescribe());
            search.setAppId(methodCodeDO.getAppId());
            search.setAppName(methodCodeDO.getAppName());
            search.setClassId(methodCodeDO.getClassId());
            search.setPackageName(methodCodeDO.getPackageName());
            search.setClassName(methodCodeDO.getClassName());
            search.setMethodId(methodCodeDO.getId());
            search.setMethodName(methodCodeDO.getMethodName());
            searchList.add(search);
        }

        if (!searchList.isEmpty()) {
            log.info("准备保存{}条代码检索记录", searchList.size());
            codeSearchManager.save(searchList);
        }
        return Boolean.TRUE;
    }

    @Override
    public QueryCodeInfoRsp queryCodeInfo(QueryCodeInfoReq req) {
        QueryCodeInfoRsp result = new QueryCodeInfoRsp();

        AppInfoDO appInfoDO = appInfoManager.get(req.getAppId());
        result.setAppDescribe(appInfoDO.getDescribe());

        if (req.getClassId() != null) {
            ClassCodeDO classCodeDO = classCodeManager.get(req.getClassId());
            result.setClassCode(classCodeDO.getClassCode());
            result.setClassDescribe(classCodeDO.getDescribe());
        }

        if (req.getMethodId() != null) {
            MethodCodeDO methodCodeDO = methodCodeManager.get(req.getMethodId());
            result.setMethodCode(methodCodeDO.getMethodCode());
            result.setMethodDescribe(methodCodeDO.getDescribe());
        }

        return result;
    }

    @Override
    public ExecuteHttpRsp executeHttpRequest(ExecuteHttpReq req) {
        HttpHeaders headers = new HttpHeaders();
        if (req.getHeaders() != null) {
            req.getHeaders().forEach(headers::add);
        }

        HttpEntity<String> entity = new HttpEntity<>(req.getBody(), headers);
        HttpMethod method = HttpMethod.resolve(req.getMethod().toUpperCase());
        if (method == null) {
            method = HttpMethod.GET;
        }

        try {
            ResponseEntity<String> response = restTemplate.exchange(req.getUrl(), method, entity, String.class);
            return ExecuteHttpRsp.builder()
                    .statusCode(response.getStatusCodeValue())
                    .headers(response.getHeaders())
                    .body(response.getBody())
                    .build();
        } catch (Exception e) {
            log.error("execute http request error", e);
            return ExecuteHttpRsp.builder()
                    .statusCode(500)
                    .body(e.getMessage())
                    .headers(Collections.emptyMap())
                    .build();
        }
    }

    /**
     * 递归收集所有java文件
     */
    private void collectJavaFiles(File dir, List<File> javaFiles) {
        if (dir == null || !dir.exists()) return;
        File[] files = dir.listFiles();
        if (files == null) return;
        for (File file : files) {
            if (file.isDirectory()) {
                collectJavaFiles(file, javaFiles);
            } else if (file.getName().endsWith(".java")) {
                javaFiles.add(file);
            }
        }
    }

    /**
     * 递归删除目录
     */
    private boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            File[] children = dir.listFiles();
            if (children != null) {
                for (File child : children) {
                    deleteDir(child);
                }
            }
        }
        return dir.delete();
    }

    private List<AppInfoDO> queryAllAppInfo() {
        AppInfoQuery query = new AppInfoQuery();
        return appInfoManager.query(query);
    }

    /**
     * 获取完整类名（父类.内部类.再内部类...）
     */
    private String getFullClassName(ClassOrInterfaceDeclaration clazz) {
        String name = clazz.getNameAsString();
        if (clazz.getParentNode().isPresent() && clazz.getParentNode().get() instanceof ClassOrInterfaceDeclaration) {
            return getFullClassName((ClassOrInterfaceDeclaration) clazz.getParentNode().get()) + "." + name;
        }
        return name;
    }

    /**
     * 递归查找所有src/main/java目录（支持多模块）
     */
    private void findSrcMainJavaDirs(File dir, List<File> result) {
        if (dir == null || !dir.exists()) return;
        if (dir.isDirectory() && dir.getName().equals("java") && dir.getParentFile() != null
            && dir.getParentFile().getName().equals("main")
            && dir.getParentFile().getParentFile() != null
            && dir.getParentFile().getParentFile().getName().equals("src")) {
            result.add(dir);
            return;
        }
        File[] files = dir.listFiles();
        if (files == null) return;
        for (File file : files) {
            findSrcMainJavaDirs(file, result);
        }
    }

}
