package com.whitebye.angel.ai.biz.service.impl;

import com.google.common.collect.Lists;
import com.whitebye.angel.ai.biz.service.AiAgentNotesService;
import com.whitebye.angel.ai.common.entity.AiAgentNotesDO;
import com.whitebye.angel.ai.common.query.AiAgentNotesQuery;
import com.whitebye.angel.ai.common.req.*;
import com.whitebye.angel.ai.common.rsp.AiAgentNotesRsp;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Deprecated
@Service
public class AiAgentNotesServiceImpl implements AiAgentNotesService {

    @Resource
    private AiAgentNotesManager aiAgentNotesManager;

    @Override
    public Boolean aiAgentNotesAdd(AiAgentNotesAddReq req) {
        if (req == null) {
            return Boolean.FALSE;
        }
        aiAgentNotesManager.addAiAgentNotes(convertFromAddReq(req));
        return Boolean.TRUE;
    }

    @Override
    public Boolean aiAgentNotesBatchAdd(AiAgentNotesBatchAddReq req) {
        if (CollectionUtils.isEmpty(req.getAddReqList())) {
            return Boolean.FALSE;
        }
        List<AiAgentNotesDO> aiAgentNotesDOList = req.getAddReqList().stream().map(this::convertFromAddReq).collect(Collectors.toList());
        aiAgentNotesManager.addAiAgentNotes(aiAgentNotesDOList);
        return Boolean.TRUE;
    }

    @Override
    public Boolean aiAgentNotesModify(AiAgentNotesModifyReq req) {
        if (req == null) {
            return Boolean.FALSE;
        }

        AiAgentNotesDO aiAgentNotesDO = new AiAgentNotesDO();
        BeanUtils.copyProperties(req, aiAgentNotesDO);
        aiAgentNotesManager.modifyAiAgentNotes(aiAgentNotesDO);
        return Boolean.TRUE;
    }

    @Override
    public Boolean aiAgentNotesBatchModify(AiAgentNotesBatchModifyReq req) {
        if (CollectionUtils.isEmpty(req.getModifyReqList())) {
            return Boolean.FALSE;
        }

        List<AiAgentNotesDO> aiAgentNotesDOList = req.getModifyReqList().stream().map(modifyReq -> {
            AiAgentNotesDO aiAgentNotesDO = new AiAgentNotesDO();
            BeanUtils.copyProperties(modifyReq, aiAgentNotesDO);
            return aiAgentNotesDO;
        }).collect(Collectors.toList());

        aiAgentNotesManager.modifyAiAgentNotes(aiAgentNotesDOList);
        return Boolean.TRUE;
    }

    @Override
    public Boolean aiAgentNotesRemove(AiAgentNotesRemoveReq req) {
        if (CollectionUtils.isEmpty(req.getIds())) {
            return Boolean.FALSE;
        }

        aiAgentNotesManager.removeAiAgentNotes(req.getIds());
        return Boolean.TRUE;
    }

    @Override
    public List<AiAgentNotesRsp> aiAgentNotesQuery(AiAgentNotesQueryReq req) {
        AiAgentNotesQuery query = new AiAgentNotesQuery();
        BeanUtils.copyProperties(req, query);

        List<AiAgentNotesDO> aiAgentNotesDOList = aiAgentNotesManager.queryAiAgentNotes(query);
        if (CollectionUtils.isEmpty(aiAgentNotesDOList)) {
            return Lists.newArrayList();
        }

        return aiAgentNotesDOList.stream().map(aiAgentNotesDO -> {
            AiAgentNotesRsp rsp = new AiAgentNotesRsp();
            BeanUtils.copyProperties(aiAgentNotesDO, rsp);
            return rsp;
        }).collect(Collectors.toList());
    }

    private AiAgentNotesDO convertFromAddReq(AiAgentNotesAddReq req) {
        AiAgentNotesDO aiAgentNotesDO = new AiAgentNotesDO();
        aiAgentNotesDO.setAgentId(req.getAgentId());
        aiAgentNotesDO.setAgentName(req.getAgentName());
        aiAgentNotesDO.setTitle(req.getTitle());
        aiAgentNotesDO.setContent(req.getContent());
        aiAgentNotesDO.setCategory(req.getCategory() == null ? "" : req.getCategory());
        aiAgentNotesDO.setTags(req.getTags() == null ? "" : req.getTags());
        aiAgentNotesDO.setPriority(req.getPriority() == null ? 50 : req.getPriority());
        aiAgentNotesDO.setCreateTime(new Date());
        aiAgentNotesDO.setUpdateTime(new Date());
        aiAgentNotesDO.setDeleted(0);
        return aiAgentNotesDO;
    }

}