package com.whitebye.angel.ai.biz.service;

import com.whitebye.angel.ai.common.req.*;
import com.whitebye.angel.ai.common.rsp.AiAgentNotesRsp;

import java.util.List;

@Deprecated
public interface AiAgentNotesService {

    /**
     * 新增笔记
     */
    Boolean aiAgentNotesAdd(AiAgentNotesAddReq req);

    /**
     * 批量新增笔记
     */
    Boolean aiAgentNotesBatchAdd(AiAgentNotesBatchAddReq req);

    /**
     * 修改笔记
     */
    Boolean aiAgentNotesModify(AiAgentNotesModifyReq req);

    /**
     * 批量修改笔记
     */
    Boolean aiAgentNotesBatchModify(AiAgentNotesBatchModifyReq req);

    /**
     * 删除笔记
     */
    Boolean aiAgentNotesRemove(AiAgentNotesRemoveReq req);

    /**
     * 查询笔记
     */
    List<AiAgentNotesRsp> aiAgentNotesQuery(AiAgentNotesQueryReq req);
} 