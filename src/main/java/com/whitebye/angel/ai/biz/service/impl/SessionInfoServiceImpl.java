package com.whitebye.angel.ai.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.whitebye.angel.ai.biz.manager.SessionInfoManager;
import com.whitebye.angel.ai.biz.service.SessionInfoService;
import com.whitebye.angel.ai.common.bo.SessionContextData;
import com.whitebye.angel.ai.common.entity.SessionInfoDO;
import com.whitebye.angel.ai.common.query.SessionInfoQuery;
import com.whitebye.angel.ai.common.req.*;
import com.whitebye.angel.ai.common.rsp.SessionInfoRsp;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;

@Deprecated
@Service
public class SessionInfoServiceImpl implements SessionInfoService {

    @Resource
    private SessionInfoManager sessionInfoManager;

    @Override
    public Boolean sessionInfoAdd(List<SessionInfoAddReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Boolean.FALSE;
        }

        reqList.forEach(this::sessionInfoAdd);
        return Boolean.TRUE;
    }

    @Override
    public Boolean sessionInfoModify(List<SessionInfoModifyReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Boolean.FALSE;
        }

        List<SessionInfoDO> sessionInfoDOList = reqList.stream().map(req -> {
            SessionInfoDO sessionInfoDO = new SessionInfoDO();
            BeanUtils.copyProperties(req, sessionInfoDO);
            return sessionInfoDO;
        }).collect(Collectors.toList());

        sessionInfoManager.modifySessionInfo(sessionInfoDOList);
        return Boolean.TRUE;
    }

    @Override
    public Boolean sessionInfoRemove(SessionInfoRemoveReq req) {
        if (CollectionUtils.isEmpty(req.getIds())) {
            return Boolean.FALSE;
        }

        sessionInfoManager.removeSessionInfo(req.getIds());
        return Boolean.TRUE;
    }

    @Override
    public List<SessionInfoRsp> sessionInfoQuery(SessionInfoQueryReq req) {
        SessionInfoQuery query = new SessionInfoQuery();
        BeanUtils.copyProperties(req, query);

        List<SessionInfoDO> sessionInfoDOList = sessionInfoManager.querySessionInfo(query);
        if (CollectionUtils.isEmpty(sessionInfoDOList)) {
            return Lists.newArrayList();
        }

        return sessionInfoDOList.stream().map(sessionInfoDO -> {
            SessionInfoRsp rsp = new SessionInfoRsp();
            BeanUtils.copyProperties(sessionInfoDO, rsp);
            return rsp;
        }).collect(Collectors.toList());
    }

    private Boolean sessionInfoAdd(SessionInfoAddReq req) {
        SessionInfoQuery sessionInfoQuery = new SessionInfoQuery();
        sessionInfoQuery.setUserId(req.getUserId());
        sessionInfoQuery.setStatus(1);
        List<SessionInfoDO> sessionInfoDOList = sessionInfoManager.querySessionInfo(sessionInfoQuery);
        if (CollectionUtils.isEmpty(sessionInfoDOList)) {
            // 初始化会话信息，会话有效时间为30分钟
            SessionInfoDO record = new SessionInfoDO();
            record.setUserId(req.getUserId());
            Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            Date now = calendar.getTime();
            record.setStartTime(now);
            calendar.add(Calendar.MINUTE, 30);
            record.setEndTime(calendar.getTime());
            record.setLatestDialogTime(now);
            record.setStatus(1);
            record.setContextData("[]");
            record.setCreateTime(now);
            record.setUpdateTime(now);
            record.setDeleted(0);
            sessionInfoManager.addSessionInfo(record);
            sessionInfoDOList = sessionInfoManager.querySessionInfo(sessionInfoQuery);
        }

        Assert.isTrue(sessionInfoDOList.size() == 1, "用户会话信息异常");
        SessionInfoDO sessionInfoDO = sessionInfoDOList.get(0);
        if (sessionInfoDO.getEndTime().before(new Date())) {
            // 会话已过期，需要重新初始化会话
            Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            Date now = calendar.getTime();
            sessionInfoDO.setStartTime(now);
            calendar.add(Calendar.MINUTE, 30);
            sessionInfoDO.setEndTime(calendar.getTime());
            sessionInfoDO.setLatestDialogTime(now);
            sessionInfoDO.setStatus(1);
            sessionInfoDO.setContextData("[]");
            sessionInfoDO.setUpdateTime(now);
            sessionInfoManager.modifySessionInfo(sessionInfoDO);
        }

        // 更新会话信息，并更新会话有效时间
        List<SessionContextData> sessionContextDataList = JSON.parseArray(sessionInfoDO.getContextData(), SessionContextData.class);
        SessionContextData sessionContextData = new SessionContextData();
        sessionContextData.setRole(req.getRole());
        sessionContextData.setOperateTime(System.currentTimeMillis());
        sessionContextData.setContent(req.getSessionData());
        sessionContextDataList.add(sessionContextData);
        sessionInfoDO.setContextData(JSON.toJSONString(sessionContextDataList));
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
        calendar.add(Calendar.MINUTE, 30);
        sessionInfoDO.setEndTime(calendar.getTime());
        sessionInfoDO.setLatestDialogTime(calendar.getTime());
        sessionInfoDO.setUpdateTime(calendar.getTime());
        sessionInfoManager.modifySessionInfo(sessionInfoDO);

        return Boolean.TRUE;
    }

} 