package com.whitebye.angel.ai.biz.service;

import com.whitebye.angel.ai.common.req.ExecuteHttpReq;
import com.whitebye.angel.ai.common.req.QueryCodeInfoReq;
import com.whitebye.angel.ai.common.rsp.ExecuteHttpRsp;
import com.whitebye.angel.ai.common.rsp.QueryCodeInfoRsp;

@Deprecated
public interface CodeService {

    Boolean analyzeCode();

    Boolean analyzeSearchCode();

    QueryCodeInfoRsp queryCodeInfo(QueryCodeInfoReq req);

    ExecuteHttpRsp executeHttpRequest(ExecuteHttpReq req);

}
