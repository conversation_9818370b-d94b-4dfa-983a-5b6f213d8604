package com.whitebye.angel.ai.biz.manager;

import com.whitebye.angel.ai.common.entity.AiAgentNotesDO;
import com.whitebye.angel.ai.common.query.AiAgentNotesQuery;
import com.whitebye.angel.ai.mapper.AiAgentNotesMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AiAgentNotesManager {

    @Resource
    private AiAgentNotesMapper aiAgentNotesMapper;

    @Transactional(rollbackFor = Exception.class)
    public Integer addAiAgentNotes(AiAgentNotesDO record) {
        if (record == null) {
            return 0;
        }
        return aiAgentNotesMapper.insert(record);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer addAiAgentNotes(List<AiAgentNotesDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return 0;
        }
        return aiAgentNotesMapper.batchInsert(recordList);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer removeAiAgentNotes(Long id) {
        return aiAgentNotesMapper.fakeDelete(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer removeAiAgentNotes(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return aiAgentNotesMapper.batchFakeDelete(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer modifyAiAgentNotes(AiAgentNotesDO record) {
        if (record == null || record.getId() == null) {
            return 0;
        }
        return aiAgentNotesMapper.updateById(record);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer modifyAiAgentNotes(List<AiAgentNotesDO> recordList) {
        if (CollectionUtils.isEmpty(recordList)) {
            return 0;
        }
        return aiAgentNotesMapper.batchUpdateByIds(recordList);
    }

    public List<AiAgentNotesDO> queryAiAgentNotes(AiAgentNotesQuery query) {
        return aiAgentNotesMapper.selectByParam(query);
    }
}