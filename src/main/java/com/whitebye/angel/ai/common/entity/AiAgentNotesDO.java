package com.whitebye.angel.ai.common.entity;

import lombok.Data;

import java.util.Date;

@Data
public class AiAgentNotesDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 智能体ID
     */
    private Long agentId;

    /**
     * 智能体名称
     */
    private String agentName;

    /**
     * 笔记标题
     */
    private String title;

    /**
     * 笔记内容
     */
    private String content;

    /**
     * 笔记分类
     */
    private String category;

    /**
     * 标签数组
     */
    private String tags;

    /**
     * 优先级(0-99,优先级最高0,优先级最低99)
     */
    private Integer priority;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0未删除 1已删除
     */
    private Integer deleted;
} 