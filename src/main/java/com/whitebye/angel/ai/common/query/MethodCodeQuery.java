package com.whitebye.angel.ai.common.query;

import lombok.Data;

@Data
public class MethodCodeQuery {

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 类ID
     */
    private Long classId;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 代码日期(版本-年月日)
     */
    private Integer codeDate;

}