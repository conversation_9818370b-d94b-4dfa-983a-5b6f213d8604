package com.whitebye.angel.ai.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.Date;
import java.util.Objects;

@Data
@ToString
@EqualsAndHashCode(of = {"appId", "packageName", "className"})
public class ClassCodeDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 类名
     */
    private String className;

    /**
     * 类代码
     */
    private String classCode;

    /**
     * 代码长度
     */
    private Integer codeLength;

    /**
     * 类型：1=类，2=接口，3=枚举，4=注解
     */
    private Integer classType;

    /**
     * 代码日期(版本-年月日)
     */
    private Integer codeDate;

    /**
     * 一段话描述
     */
    private String describe;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

}