package com.whitebye.angel.ai.common.entity;

import lombok.Data;

import java.util.Date;

/**
 * 应用信息表
 */
@Data
public class AppInfoDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 代码仓库地址
     */
    private String codeRepositoryUrl;

    /**
     * 一段话描述
     */
    private String describe;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;

}
