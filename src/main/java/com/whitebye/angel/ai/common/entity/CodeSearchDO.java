package com.whitebye.angel.ai.common.entity;

import lombok.Data;

/**
 * 代码检索表
 */
@Data
public class CodeSearchDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 一段话描述
     */
    private String describe;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 应用名
     */
    private String appName;

    /**
     * 类ID
     */
    private Long classId;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法ID
     */
    private Long methodId;

    /**
     * 方法名
     */
    private String methodName;

}
