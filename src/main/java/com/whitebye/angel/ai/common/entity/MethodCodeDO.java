package com.whitebye.angel.ai.common.entity;

import lombok.Data;
import java.util.Date;

@Data
public class MethodCodeDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 类ID
     */
    private Long classId;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 方法代码
     */
    private String methodCode;

    /**
     * 代码长度
     */
    private Integer codeLength;

    /**
     * 代码日期(版本-年月日)
     */
    private Integer codeDate;

    /**
     * 一句话描述
     */
    private String describe;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer deleted;
} 