package com.whitebye.angel.ai.common.query;

import lombok.Data;

@Data
public class CodeSearchQuery {

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 应用名
     */
    private String appName;

    /**
     * 类ID
     */
    private Long classId;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法ID
     */
    private Long methodId;

    /**
     * 方法名
     */
    private String methodName;

}
